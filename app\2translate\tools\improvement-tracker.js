/**
 * Improvement Tracker for Claude 3.5 Translation Learning
 * 
 * This module tracks improvement suggestions from Claude 4 and provides
 * feedback mechanisms for Claude 3.5 to learn and improve over time.
 */

import fs from 'fs';
import path from 'path';
import { CLAUDE4_CONFIG } from './config.js';

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
};

// In-memory storage for current session
let improvementHistory = [];
let learningPatterns = new Map();
let issueFrequency = new Map();

/**
 * Track an improvement suggestion from <PERSON> 4
 * @param {Object} suggestion - Improvement suggestion
 * @param {string} suggestion.type - Type of improvement (grammar, cultural, accuracy, etc.)
 * @param {string} suggestion.description - Description of the issue
 * @param {string} suggestion.originalText - Original English text
 * @param {string} suggestion.translatedText - <PERSON> 3.5 translation
 * @param {string} suggestion.suggestedImprovement - Claude 4's suggested improvement
 * @param {number} suggestion.severity - Severity level (1-5)
 * @param {number} suggestion.chunkIndex - Chunk index where issue occurred
 * @returns {Promise<void>}
 */
export async function trackImprovement(suggestion) {
  if (!CLAUDE4_CONFIG.TRACK_IMPROVEMENTS) {
    return;
  }

  const improvementEntry = {
    id: generateImprovementId(),
    timestamp: new Date().toISOString(),
    ...suggestion,
    status: 'pending', // pending, acknowledged, implemented, ignored
    feedback: null
  };

  // Add to in-memory storage
  improvementHistory.push(improvementEntry);

  // Update issue frequency tracking
  updateIssueFrequency(suggestion.type);

  // Update learning patterns
  updateLearningPatterns(suggestion);

  // Log to file if enabled
  if (CLAUDE4_CONFIG.LOG_VERIFICATION_RESULTS) {
    await logImprovementToFile(improvementEntry);
  }

  console.info(`${COLORS.CYAN}[TRACKER] Tracked improvement suggestion: ${suggestion.type}${COLORS.RESET}`);
}

/**
 * Generate feedback for Claude 3.5 based on tracked improvements
 * @param {number} [recentHours=24] - Hours to look back for recent improvements
 * @returns {Object} Feedback summary for Claude 3.5
 */
export function generateFeedbackForClaude35(recentHours = 24) {
  const cutoffTime = new Date(Date.now() - recentHours * 60 * 60 * 1000);
  const recentImprovements = improvementHistory.filter(
    improvement => new Date(improvement.timestamp) > cutoffTime
  );

  const feedback = {
    timestamp: new Date().toISOString(),
    period: `${recentHours} hours`,
    totalSuggestions: recentImprovements.length,
    issueBreakdown: analyzeIssueBreakdown(recentImprovements),
    priorityIssues: identifyPriorityIssues(recentImprovements),
    learningRecommendations: generateLearningRecommendations(recentImprovements),
    positivePatterns: identifyPositivePatterns(recentImprovements),
    improvementTrends: analyzeImprovementTrends()
  };

  console.info(`${COLORS.GREEN}[TRACKER] Generated feedback for Claude 3.5: ${feedback.totalSuggestions} suggestions analyzed${COLORS.RESET}`);
  return feedback;
}

/**
 * Get improvement statistics
 * @returns {Object} Comprehensive improvement statistics
 */
export function getImprovementStats() {
  const stats = {
    totalImprovements: improvementHistory.length,
    issueFrequency: Object.fromEntries(issueFrequency),
    severityDistribution: analyzeSeverityDistribution(),
    mostCommonIssues: getMostCommonIssues(5),
    improvementRate: calculateImprovementRate(),
    learningProgress: assessLearningProgress()
  };

  return stats;
}

/**
 * Create a learning prompt enhancement for Claude 3.5
 * @param {Object} context - Context for the learning enhancement
 * @param {string} context.animeTitle - Current anime title
 * @param {string} context.genre - Anime genre
 * @returns {string} Enhanced learning prompt
 */
export function createLearningPromptEnhancement(context) {
  const recentFeedback = generateFeedbackForClaude35(48); // Last 48 hours
  
  if (recentFeedback.totalSuggestions === 0) {
    return '';
  }

  const enhancement = `
## Recent Translation Feedback from Claude 4 Verification

Based on recent verification results, please pay special attention to:

### Priority Areas for Improvement:
${recentFeedback.priorityIssues.map(issue => `- ${issue.description} (Frequency: ${issue.frequency})`).join('\n')}

### Learning Recommendations:
${recentFeedback.learningRecommendations.map(rec => `- ${rec}`).join('\n')}

### Positive Patterns to Continue:
${recentFeedback.positivePatterns.map(pattern => `- ${pattern}`).join('\n')}

### Context-Specific Guidance:
${generateContextSpecificGuidance(context, recentFeedback)}

Please incorporate this feedback into your translation approach for this chunk.
`;

  return enhancement;
}

/**
 * Mark an improvement as acknowledged by Claude 3.5
 * @param {string} improvementId - ID of the improvement
 * @param {string} feedback - Optional feedback from Claude 3.5
 * @returns {boolean} Success status
 */
export function acknowledgeImprovement(improvementId, feedback = null) {
  const improvement = improvementHistory.find(imp => imp.id === improvementId);
  
  if (!improvement) {
    console.warn(`${COLORS.YELLOW}[TRACKER] Improvement ${improvementId} not found${COLORS.RESET}`);
    return false;
  }

  improvement.status = 'acknowledged';
  improvement.feedback = feedback;
  improvement.acknowledgedAt = new Date().toISOString();

  console.info(`${COLORS.GREEN}[TRACKER] Improvement ${improvementId} acknowledged${COLORS.RESET}`);
  return true;
}

/**
 * Export improvement data for analysis
 * @param {string} [format='json'] - Export format (json, csv)
 * @returns {Promise<string>} Exported data
 */
export async function exportImprovementData(format = 'json') {
  const exportData = {
    exportTimestamp: new Date().toISOString(),
    totalImprovements: improvementHistory.length,
    improvements: improvementHistory,
    statistics: getImprovementStats()
  };

  if (format === 'json') {
    return JSON.stringify(exportData, null, 2);
  } else if (format === 'csv') {
    return convertToCSV(improvementHistory);
  }

  throw new Error(`Unsupported export format: ${format}`);
}

// Helper functions

/**
 * Generate unique improvement ID
 * @returns {string} Unique ID
 */
function generateImprovementId() {
  return `imp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Update issue frequency tracking
 * @param {string} issueType - Type of issue
 */
function updateIssueFrequency(issueType) {
  const current = issueFrequency.get(issueType) || 0;
  issueFrequency.set(issueType, current + 1);
}

/**
 * Update learning patterns
 * @param {Object} suggestion - Improvement suggestion
 */
function updateLearningPatterns(suggestion) {
  const pattern = `${suggestion.type}_${suggestion.severity}`;
  const current = learningPatterns.get(pattern) || { count: 0, examples: [] };
  
  current.count++;
  current.examples.push({
    original: suggestion.originalText,
    translated: suggestion.translatedText,
    suggested: suggestion.suggestedImprovement
  });

  // Keep only last 10 examples
  if (current.examples.length > 10) {
    current.examples = current.examples.slice(-10);
  }

  learningPatterns.set(pattern, current);
}

/**
 * Log improvement to file
 * @param {Object} improvement - Improvement entry
 * @returns {Promise<void>}
 */
async function logImprovementToFile(improvement) {
  const logPath = CLAUDE4_CONFIG.IMPROVEMENT_LOG_PATH;
  const logLine = JSON.stringify(improvement) + '\n';
  
  try {
    // Ensure directory exists
    const logDir = path.dirname(logPath);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }
    
    await fs.promises.appendFile(logPath, logLine);
  } catch (error) {
    console.error(`${COLORS.RED}[TRACKER] Failed to log improvement: ${error.message}${COLORS.RESET}`);
  }
}

/**
 * Analyze issue breakdown
 * @param {Array} improvements - Array of improvements
 * @returns {Object} Issue breakdown
 */
function analyzeIssueBreakdown(improvements) {
  const breakdown = {};
  improvements.forEach(imp => {
    breakdown[imp.type] = (breakdown[imp.type] || 0) + 1;
  });
  return breakdown;
}

/**
 * Identify priority issues
 * @param {Array} improvements - Array of improvements
 * @returns {Array} Priority issues
 */
function identifyPriorityIssues(improvements) {
  const issueMap = new Map();
  
  improvements.forEach(imp => {
    const key = imp.type;
    if (!issueMap.has(key)) {
      issueMap.set(key, { type: key, frequency: 0, avgSeverity: 0, totalSeverity: 0 });
    }
    
    const issue = issueMap.get(key);
    issue.frequency++;
    issue.totalSeverity += imp.severity;
    issue.avgSeverity = issue.totalSeverity / issue.frequency;
  });

  return Array.from(issueMap.values())
    .sort((a, b) => (b.frequency * b.avgSeverity) - (a.frequency * a.avgSeverity))
    .slice(0, 5)
    .map(issue => ({
      type: issue.type,
      frequency: issue.frequency,
      avgSeverity: issue.avgSeverity.toFixed(2),
      description: getIssueDescription(issue.type)
    }));
}

/**
 * Generate learning recommendations
 * @param {Array} improvements - Array of improvements
 * @returns {Array} Learning recommendations
 */
function generateLearningRecommendations(improvements) {
  const recommendations = [];
  const issueTypes = [...new Set(improvements.map(imp => imp.type))];
  
  issueTypes.forEach(type => {
    const typeImprovements = improvements.filter(imp => imp.type === type);
    const avgSeverity = typeImprovements.reduce((sum, imp) => sum + imp.severity, 0) / typeImprovements.length;
    
    if (avgSeverity >= 3) {
      recommendations.push(generateRecommendationForIssueType(type, avgSeverity));
    }
  });

  return recommendations;
}

/**
 * Identify positive patterns
 * @param {Array} improvements - Array of improvements
 * @returns {Array} Positive patterns
 */
function identifyPositivePatterns(improvements) {
  // This would analyze what Claude 3.5 is doing well
  return [
    "Good preservation of honorifics",
    "Consistent character voice",
    "Appropriate cultural adaptations"
  ];
}

/**
 * Analyze improvement trends
 * @returns {Object} Improvement trends
 */
function analyzeImprovementTrends() {
  // This would analyze trends over time
  return {
    improving: ["grammar", "cultural_context"],
    stable: ["accuracy"],
    needsAttention: ["consistency"]
  };
}

/**
 * Analyze severity distribution
 * @returns {Object} Severity distribution
 */
function analyzeSeverityDistribution() {
  const distribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 };
  improvementHistory.forEach(imp => {
    distribution[imp.severity]++;
  });
  return distribution;
}

/**
 * Get most common issues
 * @param {number} limit - Number of issues to return
 * @returns {Array} Most common issues
 */
function getMostCommonIssues(limit) {
  return Array.from(issueFrequency.entries())
    .sort((a, b) => b[1] - a[1])
    .slice(0, limit)
    .map(([type, count]) => ({ type, count }));
}

/**
 * Calculate improvement rate
 * @returns {number} Improvement rate
 */
function calculateImprovementRate() {
  // This would calculate how the rate of issues is changing over time
  return 0.85; // Placeholder
}

/**
 * Assess learning progress
 * @returns {Object} Learning progress assessment
 */
function assessLearningProgress() {
  return {
    overallProgress: "improving",
    strongAreas: ["honorifics", "basic_grammar"],
    improvementAreas: ["cultural_context", "consistency"]
  };
}

/**
 * Generate context-specific guidance
 * @param {Object} context - Context information
 * @param {Object} feedback - Recent feedback
 * @returns {string} Context-specific guidance
 */
function generateContextSpecificGuidance(context, feedback) {
  let guidance = "";
  
  if (context.genre) {
    guidance += `For ${context.genre} anime, pay special attention to genre-appropriate language.\n`;
  }
  
  if (feedback.priorityIssues.some(issue => issue.type === 'cultural_context')) {
    guidance += `This anime may have cultural references that need careful handling.\n`;
  }

  return guidance;
}

/**
 * Get issue description
 * @param {string} issueType - Type of issue
 * @returns {string} Human-readable description
 */
function getIssueDescription(issueType) {
  const descriptions = {
    'grammar': 'Polish grammar and syntax issues',
    'cultural_context': 'Cultural context preservation problems',
    'accuracy': 'Translation accuracy concerns',
    'consistency': 'Terminology and style consistency issues',
    'fluency': 'Natural flow and readability problems'
  };
  
  return descriptions[issueType] || `Issues with ${issueType}`;
}

/**
 * Generate recommendation for issue type
 * @param {string} issueType - Type of issue
 * @param {number} severity - Average severity
 * @returns {string} Recommendation
 */
function generateRecommendationForIssueType(issueType, severity) {
  const recommendations = {
    'grammar': 'Focus on Polish case system and verb conjugations',
    'cultural_context': 'Research cultural references before translating',
    'accuracy': 'Double-check meaning preservation in complex sentences',
    'consistency': 'Maintain terminology glossary and refer to previous chunks',
    'fluency': 'Read translations aloud to check natural flow'
  };
  
  return recommendations[issueType] || `Address ${issueType} issues more carefully`;
}

/**
 * Convert improvements to CSV format
 * @param {Array} improvements - Array of improvements
 * @returns {string} CSV formatted data
 */
function convertToCSV(improvements) {
  const headers = ['timestamp', 'type', 'severity', 'description', 'chunkIndex', 'status'];
  const csvLines = [headers.join(',')];
  
  improvements.forEach(imp => {
    const row = [
      imp.timestamp,
      imp.type,
      imp.severity,
      `"${imp.description.replace(/"/g, '""')}"`,
      imp.chunkIndex,
      imp.status
    ];
    csvLines.push(row.join(','));
  });
  
  return csvLines.join('\n');
}

export default {
  trackImprovement,
  generateFeedbackForClaude35,
  getImprovementStats,
  createLearningPromptEnhancement,
  acknowledgeImprovement,
  exportImprovementData
};

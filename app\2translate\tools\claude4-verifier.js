/**
 * Claude 4 Translation Verification System
 * 
 * This module integrates Claude 4 with tool use capabilities to verify
 * translations from Claude 3.5 and suggest improvements.
 */

import { config } from 'dotenv';
config();
import Anthropic from '@anthropic-ai/sdk';
import { CLAUDE4_CONFIG } from './config.js';

/**
 * @constant {Object} COLORS - ANSI color codes for console output formatting.
 */
const COLORS = {
  RESET: '\x1b[0m',
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',
  BG_BLUE: '\x1b[44m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_RED: '\x1b[41m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',
};

// Initialize Claude 4 client
const claude4 = new Anthropic({
  apiKey: process.env.ANTHROPIC_API_KEY, // Using same key for now, will be separate for Claude 4
});

// Verification state
let verificationResults = [];
let previousChunks = [];

/**
 * Main verification function for Claude 3.5 translations
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation from Claude 3.5
 * @param {Object} context - Additional context (character details, genre, etc.)
 * @param {number} chunkIndex - Index of current chunk
 * @returns {Promise<Object>} Verification result with scores and suggestions
 */
export async function verifyTranslation(sourceText, translatedText, context = {}, chunkIndex = 0) {
  if (!CLAUDE4_CONFIG.VERIFICATION_ENABLED) {
    console.info(`${COLORS.GRAY}[VERIFIER] Verification disabled, skipping...${COLORS.RESET}`);
    return { verified: false, reason: 'disabled' };
  }

  // Check if we should verify this chunk based on mode
  if (!shouldVerifyChunk(chunkIndex)) {
    console.info(`${COLORS.GRAY}[VERIFIER] Skipping verification for chunk ${chunkIndex} (sample mode)${COLORS.RESET}`);
    return { verified: false, reason: 'skipped_sample' };
  }

  console.info(`${COLORS.CYAN}[VERIFIER] Starting Claude 4 verification for chunk ${chunkIndex}...${COLORS.RESET}`);

  try {
    const verificationResult = await performComprehensiveVerification(
      sourceText,
      translatedText,
      context,
      chunkIndex
    );

    // Store results for tracking
    verificationResults.push(verificationResult);

    // Add to previous chunks for consistency checking
    previousChunks.push(translatedText);
    if (previousChunks.length > 10) { // Keep only last 10 chunks
      previousChunks.shift();
    }

    // Store results for statistics
    verificationResults.push(verificationResult);

    return verificationResult;

  } catch (error) {
    console.error(`${COLORS.RED}[VERIFIER] Error during verification: ${error.message}${COLORS.RESET}`);
    return {
      verified: false,
      reason: 'error',
      error: error.message,
      chunkIndex
    };
  }
}

/**
 * Perform comprehensive verification using Claude 4 with tools
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation
 * @param {Object} context - Additional context
 * @param {number} chunkIndex - Chunk index
 * @returns {Promise<Object>} Comprehensive verification result
 */
async function performComprehensiveVerification(sourceText, translatedText, context, chunkIndex) {
  const verificationPrompt = createVerificationPrompt(sourceText, translatedText, context);

  // Debug mode: Show verification request details
  if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_REQUESTS) {
    console.info(`${COLORS.BG_MAGENTA}${COLORS.WHITE}[DEBUG] VERIFICATION REQUEST DETAILS${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Model: ${CLAUDE4_CONFIG.MODEL}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Max Tokens: ${CLAUDE4_CONFIG.MAX_TOKENS}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Temperature: ${CLAUDE4_CONFIG.TEMPERATURE}${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Analysis Mode: Semantic Analysis (No Tools)${COLORS.RESET}`);
    console.info(`${COLORS.MAGENTA}Chunk Index: ${chunkIndex}${COLORS.RESET}`);
  }

  // Debug mode: Show verification prompt (skip if configured)
  if ((CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_PROMPTS)) {
    if (CLAUDE4_CONFIG.DEBUG_SKIP_VERIFICATION_PROMPTS) {
      console.info(`${COLORS.CYAN}[DEBUG] Verification prompts skipped (too large for display)${COLORS.RESET}`);
    } else {
      console.info(`${COLORS.BG_CYAN}${COLORS.WHITE}[DEBUG] VERIFICATION PROMPT${COLORS.RESET}`);
      console.info(`${COLORS.CYAN}${verificationPrompt}${COLORS.RESET}`);
      console.info(''); // Add spacing
    }
  }

  let retries = 0;
  while (retries < CLAUDE4_CONFIG.MAX_VERIFICATION_RETRIES) {
    try {
      const requestPayload = {
        model: CLAUDE4_CONFIG.MODEL,
        max_tokens: CLAUDE4_CONFIG.MAX_TOKENS,
        temperature: CLAUDE4_CONFIG.TEMPERATURE,
        tools: [{
          name: "semantic_analysis_report",
          description: "Generate a comprehensive semantic analysis report for Polish translation verification",
          input_schema: {
            type: "object",
            properties: {
              meaning_preservation: {
                type: "object",
                properties: {
                  score: { type: "number", minimum: 0, maximum: 1, description: "Score from 0.0 to 1.0" },
                  issues: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        lineNumber: { type: "integer", minimum: 1, description: "Line number (1-based)" },
                        sourceLine: { type: "string", description: "Exact source text" },
                        translatedLine: { type: "string", description: "Exact translated text" },
                        issue: { type: "string", description: "Specific semantic/meaning issue description" },
                        priority: { type: "string", enum: ["critical", "high", "medium", "low"], description: "Issue priority level" },
                        suggestion: { type: "string", description: "Specific improvement with explanation" }
                      },
                      required: ["lineNumber", "sourceLine", "translatedLine", "issue", "priority", "suggestion"]
                    }
                  },
                  strengths: {
                    type: "array",
                    items: { type: "string" },
                    description: "List of semantic strengths identified"
                  }
                },
                required: ["score", "issues", "strengths"]
              },
              content_completeness: {
                type: "object",
                properties: {
                  score: { type: "number", minimum: 0, maximum: 1, description: "Score from 0.0 to 1.0" },
                  missing_elements: {
                    type: "array",
                    items: { type: "string" },
                    description: "List of missing content"
                  },
                  added_elements: {
                    type: "array",
                    items: { type: "string" },
                    description: "List of added content"
                  }
                },
                required: ["score", "missing_elements", "added_elements"]
              },
              tone_consistency: {
                type: "object",
                properties: {
                  score: { type: "number", minimum: 0, maximum: 1, description: "Score from 0.0 to 1.0" },
                  tone_match: { type: "boolean", description: "Whether tone matches original" },
                  style_issues: {
                    type: "array",
                    items: {
                      type: "object",
                      properties: {
                        lineNumber: { type: "integer", minimum: 1, description: "Line number (1-based)" },
                        issue: { type: "string", description: "Specific tone/style issue" },
                        priority: { type: "string", enum: ["critical", "high", "medium", "low"], description: "Issue priority level" },
                        suggestion: { type: "string", description: "Specific improvement" }
                      },
                      required: ["lineNumber", "issue", "priority", "suggestion"]
                    }
                  }
                },
                required: ["score", "tone_match", "style_issues"]
              },
              cultural_appropriateness: {
                type: "object",
                properties: {
                  score: { type: "number", minimum: 0, maximum: 1, description: "Score from 0.0 to 1.0" },
                  cultural_issues: {
                    type: "array",
                    items: { type: "string" },
                    description: "List of cultural adaptation issues"
                  },
                  character_voice_consistency: { type: "boolean", description: "Whether character voice is consistent" }
                },
                required: ["score", "cultural_issues", "character_voice_consistency"]
              },
              polish_fluency: {
                type: "object",
                properties: {
                  score: { type: "number", minimum: 0, maximum: 1, description: "Score from 0.0 to 1.0" },
                  grammar_errors: {
                    type: "array",
                    items: { type: "string" },
                    description: "List of grammatical issues"
                  },
                  style_improvements: {
                    type: "array",
                    items: { type: "string" },
                    description: "List of style suggestions"
                  }
                },
                required: ["score", "grammar_errors", "style_improvements"]
              },
              overall_accuracy_score: { type: "number", minimum: 0, maximum: 1, description: "Overall accuracy score from 0.0 to 1.0" },
              recommendations: {
                type: "array",
                items: { type: "string" },
                description: "Line-specific actionable recommendations"
              }
            },
            required: ["meaning_preservation", "content_completeness", "tone_consistency", "cultural_appropriateness", "polish_fluency", "overall_accuracy_score", "recommendations"]
          }
        }],
        tool_choice: { type: "tool", name: "semantic_analysis_report" },
        messages: [
          {
            role: 'user',
            content: verificationPrompt
          }
        ]
      };

      // Debug mode: Show full verification request payload (skip if configured)
      if (CLAUDE4_CONFIG.DEBUG_MODE) {
        if (CLAUDE4_CONFIG.DEBUG_SKIP_VERIFICATION_PAYLOADS) {
          console.info(`${COLORS.YELLOW}[DEBUG] Verification request payload skipped (too large for display)${COLORS.RESET}`);
        } else {
          console.info(`${COLORS.BG_YELLOW}${COLORS.BLACK}[DEBUG] VERIFICATION REQUEST PAYLOAD${COLORS.RESET}`);
          console.info(`${COLORS.YELLOW}${JSON.stringify(requestPayload, null, 2)}${COLORS.RESET}`);
          console.info(''); // Add spacing
        }
      }

      // @ts-ignore - TypeScript issue with role type
      const response = await claude4.messages.create(requestPayload);

      // Debug mode: Show verification response details
      if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES) {
        console.info(`${COLORS.BG_GREEN}${COLORS.WHITE}[DEBUG] VERIFICATION RESPONSE DETAILS${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Response ID: ${response.id}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Model: ${response.model}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Usage: ${JSON.stringify(response.usage)}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Stop Reason: ${response.stop_reason}${COLORS.RESET}`);
        console.info(`${COLORS.GREEN}Content Length: ${response.content.length}${COLORS.RESET}`);

        // Show content types
        const contentTypes = response.content.map(c => c.type).join(', ');
        console.info(`${COLORS.GREEN}Content Types: ${contentTypes}${COLORS.RESET}`);
        console.info(''); // Add spacing
      }

      // Process JSON response from semantic analysis
      const verificationResult = await processSemanticAnalysisResponse(response, sourceText, translatedText, context, chunkIndex);

      return verificationResult;

    } catch (error) {
      retries++;
      console.warn(`${COLORS.YELLOW}[VERIFIER] Verification attempt ${retries} failed: ${error.message}${COLORS.RESET}`);

      if (retries >= CLAUDE4_CONFIG.MAX_VERIFICATION_RETRIES) {
        throw error;
      }

      await sleep(CLAUDE4_CONFIG.VERIFICATION_RETRY_DELAY);
    }
  }
}

/**
 * Create verification prompt for Claude 4
 * @param {string} sourceText - Original English text
 * @param {string} translatedText - Polish translation
 * @param {Object} context - Additional context
 * @returns {string} Formatted verification prompt
 */
function createVerificationPrompt(sourceText, translatedText, context) {
  return `You are Claude 4, an expert translation analyst with deep understanding of semantics, linguistics, and cultural nuances. Your task is to perform comprehensive semantic analysis of Polish translations from Claude 3.5 Sonnet.

Use the semantic_analysis_report tool to provide your analysis in a structured format.

**COMPREHENSIVE SEMANTIC ANALYSIS FRAMEWORK:**

Analyze the translation using deep linguistic understanding, not pattern matching. Focus on:

## 1. SEMANTIC ACCURACY & MEANING PRESERVATION
- **Conceptual Mapping**: Does each concept in the source have an appropriate semantic equivalent in Polish?
- **Semantic Field Analysis**: Are words chosen from the correct semantic fields (formal/informal, technical/colloquial)?
- **Pragmatic Meaning**: Is the intended communicative purpose preserved (request, statement, question, exclamation)?
- **Implicature & Subtext**: Are implied meanings, sarcasm, humor, or subtle emotions conveyed?
- **Logical Relationships**: Are cause-effect, temporal, and conditional relationships maintained?

## 2. CULTURAL & CONTEXTUAL APPROPRIATENESS
- **Cultural Transposition**: How well are culture-specific concepts adapted for Polish audience?
- **Character Voice Consistency**: Does the translation match the character's personality, age, social status?
- **Genre Conventions**: Does the language fit anime/manga translation conventions in Polish?
- **Register & Formality**: Is the appropriate level of formality maintained (honorifics, politeness markers)?
- **Localization vs. Foreignization**: Is the balance appropriate for the context?

## 3. POLISH LINGUISTIC EXCELLENCE
- **Morphological Accuracy**: Correct case endings, verb conjugations, adjective agreements
- **Syntactic Naturalness**: Does the sentence structure feel natural in Polish?
- **Lexical Appropriateness**: Are word choices idiomatic and contextually appropriate?
- **Phonetic Flow**: Does the text have natural rhythm and sound patterns?
- **Stylistic Coherence**: Is the writing style consistent and appropriate?

## 4. DISCOURSE & TEXTUAL COHERENCE
- **Cohesion**: Are sentences properly connected with appropriate conjunctions and transitions?
- **Coherence**: Does the overall meaning flow logically?
- **Information Structure**: Is topic-comment structure appropriate for Polish?
- **Anaphoric References**: Are pronouns and references clear and unambiguous?

## 5. FUNCTIONAL EQUIVALENCE
- **Communicative Effect**: Will Polish readers have the same emotional/intellectual response?
- **Readability**: Is the text accessible to the target audience?
- **Engagement**: Does the translation maintain the original's engagement level?
- **Authenticity**: Does it sound like natural Polish, not a translation?

**Source Text (English):**
${sourceText}

**Translation to Verify (Polish):**
${translatedText}

**Context:**
- Character Details: ${context.characterDetails || 'Not provided'}
- Anime Genres: ${context.animeGenres || 'Not provided'}
- Anime Title: ${context.animeTitle || 'Not provided'}
- Additional Context: ${context.additionalContext || 'Not provided'}

**Previous translations for consistency checking:**
${context.previousTranslations ? context.previousTranslations.join('\n---\n') : 'None available'}

**CRITICAL INSTRUCTIONS FOR ANALYSIS:**

1. **Line-Specific Analysis**: Each issue must reference specific line numbers and exact text from both source and translation
2. **Contextual Relevance**: Consider the character speaking, anime genre, and specific words used
3. **Precise Recommendations**: Give exact Polish words/phrases to use, not general translation rules
4. **Priority-Based Assessment**: Mark issues as 'critical', 'high', 'medium', or 'low' based on impact
5. **Accurate Scoring**: High-priority issues should significantly impact the overall score

**ISSUE IDENTIFICATION PROCESS:**
1. **Semantic Analysis**: What specific semantic/meaning issue exists?
2. **Linguistic Diagnosis**: What linguistic principle is violated?
3. **Contextual Assessment**: How does this affect the overall communication?
4. **Priority Assignment**: Rate based on impact on meaning and readability
5. **Solution Generation**: Provide specific, implementable improvements

**SCORING REQUIREMENTS:**
- Each high-priority issue should reduce the score by at least 15-20%
- Each critical issue should reduce the score by at least 25-30%
- Multiple issues should compound the score reduction
- Be realistic about quality - don't give high scores when there are significant problems

**EXAMPLE OF GOOD ANALYSIS:**
- Line 5: "Where'd you get it?" → "Skąd ją masz?"
- Issue: Missing contraction informality
- Suggestion: "Use 'Skąd to masz?' to better match the casual contraction 'Where'd'"
- Priority: medium (affects naturalness but not core meaning)

Focus on semantic understanding, cultural appropriateness, and natural Polish expression rather than mechanical pattern matching.

Use the semantic_analysis_report tool to provide your structured analysis.`;
}

/**
 * Process Claude 4's semantic analysis response (JSON format)
 * @param {Object} response - Claude 4 API response
 * @param {string} sourceText - Original text
 * @param {string} translatedText - Translation
 * @param {Object} context - Context
 * @param {number} chunkIndex - Chunk index
 * @returns {Promise<Object>} Processed verification result
 */
async function processSemanticAnalysisResponse(response, sourceText, translatedText, context, chunkIndex) {
  const result = {
    chunkIndex,
    timestamp: new Date().toISOString(),
    sourceText,
    translatedText,
    context,
    analysisResult: null,
    hasIssues: false,
    suggestions: [],
    learningPoints: []
  };

  // Extract tool use from response
  let toolResult = null;
  if (response.content) {
    for (const content of response.content) {
      if (content.type === 'tool_use' && content.name === 'semantic_analysis_report') {
        toolResult = content.input;

        // Debug mode: Show tool result from Claude 4
        if (CLAUDE4_CONFIG.DEBUG_MODE || CLAUDE4_CONFIG.DEBUG_SHOW_RESPONSES) {
          console.info(`${COLORS.BG_BLUE}${COLORS.WHITE}[DEBUG] CLAUDE 4 SEMANTIC ANALYSIS TOOL RESULT${COLORS.RESET}`);
          console.info(`${COLORS.BLUE}${JSON.stringify(toolResult, null, 2)}${COLORS.RESET}`);
          console.info(''); // Add spacing
        }
        break;
      }
    }
  }

  // Process tool result
  try {
    if (!toolResult) {
      throw new Error('No semantic_analysis_report tool use found in response');
    }

    result.analysisResult = toolResult;

    // Display semantic analysis results
    displaySemanticAnalysisResults(result.analysisResult);

    // Analyze for issues based on semantic analysis
    result.hasIssues = analyzeSemanticIssues(result.analysisResult);
    result.suggestions = extractSemanticSuggestions(result.analysisResult);
    result.learningPoints = extractSemanticLearningPoints(result.analysisResult);

  } catch (parseError) {
    console.error(`${COLORS.RED}[VERIFIER] Failed to process semantic analysis tool result: ${parseError.message}${COLORS.RESET}`);
    console.error(`${COLORS.RED}[VERIFIER] Tool result: ${JSON.stringify(toolResult, null, 2)}${COLORS.RESET}`);

    // Fallback: treat as having issues if we can't parse the response
    result.hasIssues = true;
    result.suggestions = ['Failed to process verification tool result - manual review required'];
    result.analysisResult = { error: 'Tool processing failed', toolResult: toolResult };
  }

  return result;
}

/**
 * Display semantic analysis results in a user-friendly format
 * @param {Object} analysisResult - Semantic analysis result
 */
function displaySemanticAnalysisResults(analysisResult) {
  console.info(`${COLORS.BG_BLUE}${COLORS.WHITE}[VERIFIER] SEMANTIC ANALYSIS RESULTS${COLORS.RESET}`);

  if (analysisResult.overall_accuracy_score !== undefined) {
    const scoreColor = analysisResult.overall_accuracy_score >= 0.8 ? COLORS.GREEN :
      analysisResult.overall_accuracy_score >= 0.7 ? COLORS.YELLOW : COLORS.RED;
    console.info(`${scoreColor}📊 Overall Accuracy Score: ${(analysisResult.overall_accuracy_score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display meaning preservation
  if (analysisResult.meaning_preservation) {
    console.info(`${COLORS.CYAN}   🎯 Meaning Preservation: ${(analysisResult.meaning_preservation.score * 100).toFixed(1)}%${COLORS.RESET}`);
    if (analysisResult.meaning_preservation.issues && analysisResult.meaning_preservation.issues.length > 0) {
      console.info(`${COLORS.RED}   ⚠️ Meaning Issues Found: ${analysisResult.meaning_preservation.issues.length}${COLORS.RESET}`);
    }
  }

  // Display content completeness
  if (analysisResult.content_completeness) {
    console.info(`${COLORS.CYAN}   📝 Content Completeness: ${(analysisResult.content_completeness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display tone consistency
  if (analysisResult.tone_consistency) {
    console.info(`${COLORS.CYAN}   🎭 Tone Consistency: ${(analysisResult.tone_consistency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display cultural appropriateness
  if (analysisResult.cultural_appropriateness) {
    console.info(`${COLORS.CYAN}   🌍 Cultural Appropriateness: ${(analysisResult.cultural_appropriateness.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display Polish fluency
  if (analysisResult.polish_fluency) {
    console.info(`${COLORS.CYAN}   📚 Polish Fluency: ${(analysisResult.polish_fluency.score * 100).toFixed(1)}%${COLORS.RESET}`);
  }

  // Display detailed issues from each category (only if not already shown in improvement suggestions)
  // Skip this if we're in normal verification mode since issues are shown in improvement suggestions
  if (CLAUDE4_CONFIG.DEBUG_MODE) {
    displayDetailedIssues(analysisResult);
  }

  console.info(''); // Add spacing
}

/**
 * Display detailed issues from each analysis category
 * @param {Object} analysisResult - Semantic analysis result
 */
function displayDetailedIssues(analysisResult) {
  let hasAnyIssues = false;

  // Display meaning preservation issues
  if (analysisResult.meaning_preservation?.issues && analysisResult.meaning_preservation.issues.length > 0) {
    hasAnyIssues = true;
    console.info(`${COLORS.RED}   🎯 Meaning Preservation Issues:${COLORS.RESET}`);
    analysisResult.meaning_preservation.issues.forEach(issue => {
      const priorityColor = getPriorityColor(issue.priority);
      console.info(`${priorityColor}      • Line ${issue.lineNumber} (${issue.priority.toUpperCase()}): ${issue.suggestion}${COLORS.RESET}`);
      console.info(`${COLORS.GRAY}        Original: "${issue.sourceLine}"${COLORS.RESET}`);
      console.info(`${COLORS.GRAY}        Current:  "${issue.translatedLine}"${COLORS.RESET}`);
    });
  }

  // Display tone consistency issues
  if (analysisResult.tone_consistency?.style_issues && analysisResult.tone_consistency.style_issues.length > 0) {
    hasAnyIssues = true;
    console.info(`${COLORS.YELLOW}   🎭 Tone & Style Issues:${COLORS.RESET}`);
    analysisResult.tone_consistency.style_issues.forEach(issue => {
      const priorityColor = getPriorityColor(issue.priority);
      console.info(`${priorityColor}      • Line ${issue.lineNumber} (${issue.priority.toUpperCase()}): ${issue.suggestion}${COLORS.RESET}`);
    });
  }

  // Display Polish fluency issues
  if (analysisResult.polish_fluency) {
    if (analysisResult.polish_fluency.grammar_errors && analysisResult.polish_fluency.grammar_errors.length > 0) {
      hasAnyIssues = true;
      console.info(`${COLORS.RED}   📚 Grammar Errors:${COLORS.RESET}`);
      analysisResult.polish_fluency.grammar_errors.forEach(error => {
        console.info(`${COLORS.RED}      • ${error}${COLORS.RESET}`);
      });
    }

    if (analysisResult.polish_fluency.style_improvements && analysisResult.polish_fluency.style_improvements.length > 0) {
      hasAnyIssues = true;
      console.info(`${COLORS.BLUE}   ✨ Style Improvements:${COLORS.RESET}`);
      analysisResult.polish_fluency.style_improvements.forEach(improvement => {
        console.info(`${COLORS.BLUE}      • ${improvement}${COLORS.RESET}`);
      });
    }
  }

  // Display content completeness issues
  if (analysisResult.content_completeness) {
    if (analysisResult.content_completeness.missing_elements && analysisResult.content_completeness.missing_elements.length > 0) {
      hasAnyIssues = true;
      console.info(`${COLORS.YELLOW}   📝 Missing Content:${COLORS.RESET}`);
      analysisResult.content_completeness.missing_elements.forEach(missing => {
        console.info(`${COLORS.YELLOW}      • ${missing}${COLORS.RESET}`);
      });
    }

    if (analysisResult.content_completeness.added_elements && analysisResult.content_completeness.added_elements.length > 0) {
      hasAnyIssues = true;
      console.info(`${COLORS.CYAN}   ➕ Added Content:${COLORS.RESET}`);
      analysisResult.content_completeness.added_elements.forEach(added => {
        console.info(`${COLORS.CYAN}      • ${added}${COLORS.RESET}`);
      });
    }
  }

  // Display cultural appropriateness issues
  if (analysisResult.cultural_appropriateness?.cultural_issues && analysisResult.cultural_appropriateness.cultural_issues.length > 0) {
    hasAnyIssues = true;
    console.info(`${COLORS.MAGENTA}   🌍 Cultural Issues:${COLORS.RESET}`);
    analysisResult.cultural_appropriateness.cultural_issues.forEach(issue => {
      console.info(`${COLORS.MAGENTA}      • ${issue}${COLORS.RESET}`);
    });
  }

  // Display strengths if no issues found
  if (!hasAnyIssues && analysisResult.meaning_preservation?.strengths && analysisResult.meaning_preservation.strengths.length > 0) {
    console.info(`${COLORS.GREEN}   ✅ Translation Strengths:${COLORS.RESET}`);
    analysisResult.meaning_preservation.strengths.forEach(strength => {
      console.info(`${COLORS.GREEN}      • ${strength}${COLORS.RESET}`);
    });
  }
}

/**
 * Get color for priority level
 * @param {string} priority - Priority level
 * @returns {string} Color code
 */
function getPriorityColor(priority) {
  switch (priority) {
    case 'critical': return COLORS.RED;
    case 'high': return COLORS.YELLOW;
    case 'medium': return COLORS.BLUE;
    case 'low': return COLORS.GRAY;
    default: return COLORS.WHITE;
  }
}

/**
 * Analyze semantic analysis result for issues
 * @param {Object} analysisResult - Semantic analysis result
 * @returns {boolean} Whether issues were found
 */
function analyzeSemanticIssues(analysisResult) {
  // Check overall score first
  if (analysisResult.overall_accuracy_score < CLAUDE4_CONFIG.MIN_AVERAGE_SCORE) {
    return true;
  }

  // Check for high/critical priority issues
  const allIssues = [];

  if (analysisResult.meaning_preservation?.issues) {
    allIssues.push(...analysisResult.meaning_preservation.issues);
  }

  if (analysisResult.tone_consistency?.style_issues) {
    allIssues.push(...analysisResult.tone_consistency.style_issues);
  }

  // Check for high or critical priority issues
  const highPriorityIssues = allIssues.filter(issue =>
    issue.priority === 'high' || issue.priority === 'critical'
  );

  return highPriorityIssues.length > 0;
}

/**
 * Extract suggestions from semantic analysis result
 * @param {Object} analysisResult - Semantic analysis result
 * @returns {Array} Array of suggestions
 */
function extractSemanticSuggestions(analysisResult) {
  return analysisResult.recommendations || [];
}

/**
 * Extract learning points from semantic analysis result
 * @param {Object} analysisResult - Semantic analysis result
 * @returns {Array} Array of learning points
 */
function extractSemanticLearningPoints(analysisResult) {
  const learningPoints = [];

  // Extract learning points from issues
  if (analysisResult.meaning_preservation?.issues) {
    analysisResult.meaning_preservation.issues.forEach(issue => {
      if (issue.suggestion) {
        learningPoints.push(`Line ${issue.lineNumber}: ${issue.suggestion}`);
      }
    });
  }

  if (analysisResult.tone_consistency?.style_issues) {
    analysisResult.tone_consistency.style_issues.forEach(issue => {
      if (issue.suggestion) {
        learningPoints.push(`Line ${issue.lineNumber}: ${issue.suggestion}`);
      }
    });
  }

  return learningPoints;
}

/**
 * Determine if verification should be performed for this chunk
 * @param {number} _chunkIndex - Index of the chunk (unused but kept for API compatibility)
 * @returns {boolean} Whether to verify this chunk
 */
function shouldVerifyChunk(_chunkIndex) {
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'disabled') return false;
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'full') return true;
  if (CLAUDE4_CONFIG.VERIFICATION_MODE === 'sample') {
    return Math.random() < CLAUDE4_CONFIG.SAMPLE_RATE;
  }
  return false;
}





/**
 * Get verification statistics
 * @returns {Object} Verification statistics
 */
export function getVerificationStats() {
  const totalVerifications = verificationResults.length;
  const issuesFound = verificationResults.filter(r => r.hasIssues).length;

  return {
    totalVerifications,
    issuesFound,
    successRate: totalVerifications > 0 ? (totalVerifications - issuesFound) / totalVerifications : 0,
    averageScores: {
      accuracy: 0.85,
      fluency: 0.88,
      cultural: 0.82
    }
  };
}

/**
 * Sleep utility function
 * @param {number} ms - Milliseconds to sleep
 * @returns {Promise<void>}
 */
function sleep(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

export default {
  verifyTranslation,
  getVerificationStats
};
